<!DOCTYPE html>
<html>
<head>
    <title>Voyager AI - Explore the Universe of Conversation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Dark theme (nebula purple focused) */
            --bg-color: #0a0a0f;
            --text-color: #e8e3ff;
            --primary-color: #7b68ee;
            --secondary-color: #9370db;
            --accent-color: #da70d6;
            --tertiary-color: #8a2be2;
            --button-bg: linear-gradient(135deg, #7b68ee, #9370db);
            --button-hover: linear-gradient(135deg, #9370db, #8a2be2);
            --container-bg: rgba(75, 0, 130, 0.15);
            --box-shadow: rgba(123, 104, 238, 0.3);
            --transition-speed: 0.4s;
            --star-color: #e8e3ff;
            --nebula-color: rgba(123, 104, 238, 0.4);
            --nebula-secondary: rgba(147, 112, 219, 0.3);
            --nebula-tertiary: rgba(138, 43, 226, 0.2);
            --glow-color: rgba(123, 104, 238, 0.6);
        }

        /* Light theme (cosmic lavender) */
        :root.light-theme {
            --bg-color: #f8f6ff;
            --text-color: #4a148c;
            --primary-color: #8e24aa;
            --secondary-color: #ab47bc;
            --accent-color: #ce93d8;
            --tertiary-color: #7b1fa2;
            --button-bg: linear-gradient(135deg, #ab47bc, #ce93d8);
            --button-hover: linear-gradient(135deg, #8e24aa, #ab47bc);
            --container-bg: rgba(206, 147, 216, 0.2);
            --box-shadow: rgba(142, 36, 170, 0.2);
            --star-color: #8e24aa;
            --nebula-color: rgba(206, 147, 216, 0.3);
            --nebula-secondary: rgba(171, 71, 188, 0.2);
            --nebula-tertiary: rgba(142, 36, 170, 0.15);
            --glow-color: rgba(142, 36, 170, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: all var(--transition-speed) ease;
        }

        body {
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            overflow: hidden;
            position: relative;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
            opacity: 0;
            animation: cosmicFadeIn 1.5s ease-out forwards;
        }

        @keyframes cosmicFadeIn {
            from {
                opacity: 0;
                transform: scale(1.05);
                filter: blur(2px);
            }
            to {
                opacity: 1;
                transform: scale(1);
                filter: blur(0px);
            }
        }

        /* Enhanced space background with multiple nebula layers */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(ellipse 800px 600px at 20% 30%, var(--nebula-color) 0%, transparent 50%),
                radial-gradient(ellipse 600px 400px at 80% 70%, var(--nebula-secondary) 0%, transparent 50%),
                radial-gradient(ellipse 400px 300px at 50% 20%, var(--nebula-tertiary) 0%, transparent 50%),
                var(--bg-color);
            z-index: -2;
            transition: background var(--transition-speed) ease;
            animation: nebulaFloat 20s ease-in-out infinite;
        }

        @keyframes nebulaFloat {
            0%, 100% {
                filter: hue-rotate(0deg) brightness(1);
            }
            33% {
                filter: hue-rotate(10deg) brightness(1.1);
            }
            66% {
                filter: hue-rotate(-10deg) brightness(0.9);
            }
        }

        /* Stars */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: var(--star-color);
            border-radius: 50%;
            animation: cosmicTwinkle 3s ease-in-out infinite;
            transition: background-color var(--transition-speed) ease;
            box-shadow: 0 0 6px var(--glow-color);
        }

        .star.small {
            width: 1px;
            height: 1px;
            animation-duration: 2.5s;
        }

        .star.medium {
            width: 2px;
            height: 2px;
            animation-duration: 3.5s;
            box-shadow: 0 0 8px var(--glow-color);
        }

        .star.large {
            width: 3px;
            height: 3px;
            animation-duration: 4s;
            box-shadow: 0 0 12px var(--glow-color);
        }

        @keyframes cosmicTwinkle {
            0%, 100% {
                opacity: 0.4;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* Enhanced spacecraft animation */
        .spacecraft {
            position: absolute;
            top: 20%;
            left: -100px;
            font-size: 60px;
            color: var(--primary-color);
            animation: cosmicFly 20s ease-in-out infinite;
            z-index: 1;
            transition: color var(--transition-speed) ease;
            filter: drop-shadow(0 0 20px var(--glow-color));
        }

        @keyframes cosmicFly {
            0% {
                left: -100px;
                top: 20%;
                transform: rotate(0deg) scale(1);
                opacity: 0.8;
            }
            15% {
                opacity: 1;
                transform: rotate(8deg) scale(1.1);
            }
            25% {
                left: 25%;
                top: 15%;
                transform: rotate(12deg) scale(1.2);
            }
            50% {
                left: 50%;
                top: 25%;
                transform: rotate(-8deg) scale(1.1);
            }
            75% {
                left: 75%;
                top: 10%;
                transform: rotate(5deg) scale(1.15);
            }
            85% {
                opacity: 1;
                transform: rotate(-3deg) scale(1);
            }
            100% {
                left: calc(100% + 100px);
                top: 20%;
                transform: rotate(0deg) scale(0.8);
                opacity: 0.6;
            }
        }

        /* Enhanced theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--container-bg);
            border: 2px solid var(--primary-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 12px;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease, transform 0.3s ease;
            z-index: 10;
            box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 20px var(--glow-color);
            backdrop-filter: blur(10px);
            animation: cosmicPulse 4s ease-in-out infinite;
        }

        @keyframes cosmicPulse {
            0%, 100% {
                box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 20px var(--glow-color);
            }
            50% {
                box-shadow: 0 6px 30px var(--box-shadow), inset 0 0 30px var(--glow-color);
            }
        }

        .theme-toggle:hover {
            transform: scale(1.15) rotate(180deg);
            background: var(--button-bg);
            color: white;
            box-shadow: 0 8px 40px var(--glow-color), inset 0 0 40px rgba(255, 255, 255, 0.2);
            animation: none;
        }

        .theme-toggle i {
            transition: transform var(--transition-speed) ease;
            filter: drop-shadow(0 0 8px var(--glow-color));
        }

        /* Enhanced logout button */
        .logout-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: var(--container-bg);
            border: 2px solid var(--accent-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 14px;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all var(--transition-speed) ease, transform 0.3s ease;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 15px var(--glow-color);
            backdrop-filter: blur(10px);
            animation: cosmicGlow 3s ease-in-out infinite alternate;
        }

        @keyframes cosmicGlow {
            0% {
                box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 15px var(--glow-color);
            }
            100% {
                box-shadow: 0 6px 25px var(--box-shadow), inset 0 0 25px var(--glow-color);
            }
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
            color: white;
            transform: scale(1.1) translateY(-3px);
            box-shadow: 0 8px 30px var(--glow-color), inset 0 0 30px rgba(255, 255, 255, 0.2);
            animation: none;
        }

        /* Main container */
        .landing-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            padding: 20px;
            position: relative;
            z-index: 2;
        }

        .hero-section {
            background: var(--container-bg);
            border-radius: 25px;
            padding: 60px 40px;
            box-shadow: 0 25px 50px var(--box-shadow), inset 0 0 30px var(--glow-color);
            backdrop-filter: blur(15px);
            border: 2px solid var(--primary-color);
            max-width: 600px;
            width: 100%;
            transition: all var(--transition-speed) ease;
            animation: gentleFloat 10s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, var(--glow-color), transparent);
            animation: gentleSweep 12s linear infinite;
            opacity: 0.05;
        }

        .chat-box {
            background: var(--container-bg);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px var(--box-shadow), inset 0 0 30px var(--glow-color);
            backdrop-filter: blur(15px);
            border: 2px solid var(--secondary-color);
            max-width: 400px;
            width: 100%;
            text-align: center;
            transition: all var(--transition-speed) ease;
            animation: gentleFloat 8s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .chat-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, var(--glow-color), transparent);
            animation: gentleSweep 15s linear infinite;
            opacity: 0.05;
        }

        @keyframes gentleFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        @keyframes gentleSweep {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .chat-box h2 {
            font-size: 32px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .chat-box .btn {
            font-size: 20px;
            padding: 20px 40px;
            width: 100%;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
            letter-spacing: -0.02em;
        }

        .tagline {
            font-size: 24px;
            margin-bottom: 15px;
            color: var(--text-color);
            font-weight: 400;
            letter-spacing: -0.01em;
        }

        .description {
            font-size: 16px;
            margin-bottom: 40px;
            color: var(--text-color);
            opacity: 0.85;
            line-height: 1.7;
            font-weight: 300;
            letter-spacing: 0.01em;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 18px 35px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all var(--transition-speed) ease, transform 0.3s ease;
            min-width: 180px;
            justify-content: center;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.02em;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--button-bg);
            color: white;
            box-shadow: 0 6px 25px var(--glow-color), inset 0 0 20px rgba(255, 255, 255, 0.1);
            border: 2px solid var(--primary-color);
        }

        .btn-primary:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 12px 40px var(--glow-color), inset 0 0 30px rgba(255, 255, 255, 0.2);
            background: var(--button-hover);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-color);
            border: 2px solid var(--secondary-color);
            box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 15px var(--glow-color);
        }

        .btn-secondary:hover {
            background: var(--button-bg);
            color: white;
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 12px 40px var(--glow-color), inset 0 0 30px rgba(255, 255, 255, 0.2);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .hero-section {
                padding: 40px 20px;
                margin: 20px;
            }

            .logo {
                font-size: 36px;
            }

            .tagline {
                font-size: 20px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
            }

            .spacecraft {
                font-size: 40px;
            }
        }

        /* Enhanced floating particles */
        .particle {
            position: absolute;
            background: radial-gradient(circle, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            opacity: 0.7;
            animation: cosmicFloat 8s ease-in-out infinite;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 0 15px var(--glow-color);
        }

        @keyframes cosmicFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.7;
            }
            33% {
                transform: translateY(-30px) rotate(120deg) scale(1.2);
                opacity: 1;
            }
            66% {
                transform: translateY(-15px) rotate(240deg) scale(0.8);
                opacity: 0.5;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
        <i class="fas fa-moon"></i>
    </button>


    <div class="space-background"></div>
    <div class="stars" id="stars"></div>

    <div class="spacecraft">
        <i class="fas fa-rocket"></i>
    </div>

    <div class="landing-container">
        {% if is_logged_in %}
            <div class="chat-box">
                <h2>
                    <i class="fas fa-rocket"></i> Voyager
                </h2>
                <p class="description">
                    Your AI companion for exploring the infinite cosmos of knowledge and conversation.
                </p>
                <div class="action-buttons">
                    <a href="{{ url_for('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </a>
                    <a href="{{ url_for('register') }}" class="btn btn-secondary">
                        <i class="fas fa-user-plus"></i>
                        Sign Up
                    </a>
                </div>
            </div>
        {% else %}
            <div class="hero-section">
                <div class="logo">
                    <i class="fas fa-robot"></i> Voyager AI
                </div>
                <h1 class="tagline">Explore the Universe of Conversation</h1>
                <p class="description">
                    Embark on an interstellar journey of knowledge and discovery.
                    Chat with our advanced AI companion and explore limitless possibilities
                    across the cosmos of information.
                </p>
                <div class="action-buttons">
                    <a href="{{ url_for('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </a>
                    <a href="{{ url_for('register') }}" class="btn btn-secondary">
                        <i class="fas fa-user-plus"></i>
                        Sign Up
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <script>
        // Create enhanced cosmic stars
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const starCount = 200;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random size with weighted distribution
                const sizeRandom = Math.random();
                let size;
                if (sizeRandom < 0.7) size = 'small';
                else if (sizeRandom < 0.9) size = 'medium';
                else size = 'large';

                star.classList.add(size);

                // Random position
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // Random animation delay and duration
                star.style.animationDelay = Math.random() * 4 + 's';
                star.style.animationDuration = (2.5 + Math.random() * 2) + 's';

                starsContainer.appendChild(star);
            }
        }

        // Create enhanced cosmic particles
        function createParticles() {
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size with cosmic variation
                const size = Math.random() * 6 + 3;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random position
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';

                // Random animation properties for more cosmic movement
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 6 + 6) + 's';

                // Add random rotation for more dynamic movement
                particle.style.transform = `rotate(${Math.random() * 360}deg)`;

                document.body.appendChild(particle);
            }
        }

        // Enhanced theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');
            const button = this;

            // Add cosmic transition effect
            button.style.transform = 'scale(0.8) rotate(360deg)';
            button.style.filter = 'brightness(1.5)';

            // Create cosmic ripple effect
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                width: 0;
                height: 0;
                border-radius: 50%;
                background: radial-gradient(circle, var(--glow-color), transparent);
                transform: translate(-50%, -50%);
                pointer-events: none;
                z-index: -1;
                animation: cosmicRipple 0.6s ease-out;
            `;

            button.appendChild(ripple);

            setTimeout(() => {
                if (root.classList.contains('light-theme')) {
                    // Switch to dark theme
                    root.classList.remove('light-theme');
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                    localStorage.setItem('theme', 'dark');
                } else {
                    // Switch to light theme
                    root.classList.add('light-theme');
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                    localStorage.setItem('theme', 'light');
                }

                // Reset button transform
                setTimeout(() => {
                    button.style.transform = '';
                    button.style.filter = '';
                    ripple.remove();
                }, 200);
            }, 200);
        });

        // Add cosmic ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes cosmicRipple {
                0% {
                    width: 0;
                    height: 0;
                    opacity: 1;
                }
                100% {
                    width: 200px;
                    height: 200px;
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Load theme preference on page load
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const root = document.documentElement;

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // Initialize animations
            createStars();
            createParticles();
        });
    </script>
</body>
</html>
