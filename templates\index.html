﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager AI - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            /* Dark theme (default) */
            --bg-color: #1a1a1a;
            --text-color: #ffffff;
            --sidebar-bg: #262626;
            --sidebar-border: #333;
            --container-bg: #2d2d2d;
            --input-bg: #404040;
            --input-container-bg: #262626;
            --user-message-bg: #404040;
            --bot-message-bg: #333333;
            --bot-message-text: #e0e0e0;
            --chat-item-bg: #333;
            --chat-item-hover: #404040;
            --user-info-bg: #333;
            --button-bg: #404040;
            --button-hover: #505050;
            --scrollbar-track: #2d2d2d;
            --scrollbar-thumb: #666666;
            --scrollbar-thumb-hover: #808080;
            --modal-bg: #2c2c2c;
            --active-chat-bg: #4a4a4a;
            --active-chat-border: #007bff;
            --chat-menu-bg: #404040;
            --chat-menu-hover: #505050;
            --notification-success: #28a745;
            --notification-error: #dc3545;
            --notification-info: #17a2b8;
            --delete-btn: #dc3545;
            --cancel-btn: #6c757d;
            --saved-bg: #2c3e50;
            --star-color: #ffd700;
            --transition-speed: 0.3s;
        }

        /* Light theme (gray and white) */
        :root.light-theme {
            --bg-color: #f5f5f5;
            --text-color: #333333;
            --sidebar-bg: #e0e0e0;
            --sidebar-border: #bdbdbd;
            --container-bg: #ffffff;
            --input-bg: #f0f0f0;
            --input-container-bg: #e0e0e0;
            --user-message-bg: #e0e0e0;
            --bot-message-bg: #f0f0f0;
            --bot-message-text: #333333;
            --chat-item-bg: #e0e0e0;
            --chat-item-hover: #bdbdbd;
            --user-info-bg: #bdbdbd;
            --button-bg: #e0e0e0;
            --button-hover: #bdbdbd;
            --scrollbar-track: #f5f5f5;
            --scrollbar-thumb: #e0e0e0;
            --scrollbar-thumb-hover: #bdbdbd;
            --modal-bg: #f5f5f5;
            --active-chat-bg: #bdbdbd;
            --active-chat-border: #757575;
            --chat-menu-bg: #f5f5f5;
            --chat-menu-hover: #e0e0e0;
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --delete-btn: #ef5350;
            --cancel-btn: #9e9e9e;
            --saved-bg: #9e9e9e;
            --star-color: #ffc107;
            --transition-speed: 0.3s;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            padding: 20px;
            margin: 0;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        .sidebar {
            width: 280px;
            background-color: var(--sidebar-bg);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            overflow-y: auto;
            position: relative;
            z-index: 100;
            border-right: 1px solid var(--sidebar-border);
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
            border-radius: 15px;
            margin-right: 20px;
        }

        .sidebar-header {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.8em;
            font-weight: bold;
            color: var(--text-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-header i {
            margin-right: 10px;
            color: #007bff;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
        }

        .sidebar-menu li {
            margin-bottom: 10px;
        }

        .sidebar-menu a {
            color: var(--text-color);
            text-decoration: none;
            padding: 12px 15px;
            border-radius: 8px;
            transition: background-color var(--transition-speed) ease;
            display: flex;
            align-items: center;
        }

        .sidebar-menu a:hover {
            background-color: var(--chat-item-hover);
        }

        .sidebar-menu a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background-color: var(--container-bg);
            border-radius: 15px;
            overflow: hidden;
            transition: background-color var(--transition-speed) ease;
        }

        .chat-header {
            background-color: var(--input-container-bg);
            color: var(--text-color);
            padding: 15px 20px;
            font-size: 1.4em;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 50;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        .chat-area {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: var(--bg-color);
            display: flex;
            flex-direction: column;
            transition: background-color var(--transition-speed) ease;
        }

        .message-container {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }

        .message-container.user {
            justify-content: flex-end;
        }

        .message-container.ai {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 18px;
            border-radius: 20px;
            line-height: 1.5;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .message-bubble.user {
            background-color: var(--user-message-bg);
            color: var(--text-color);
            border-bottom-right-radius: 5px;
        }

        .message-bubble.ai {
            background-color: var(--bot-message-bg);
            color: var(--bot-message-text);
            border-bottom-left-radius: 5px;
        }

        .chat-input-area {
            padding: 15px 20px;
            background-color: var(--input-container-bg);
            border-top: 1px solid var(--sidebar-border);
            display: flex;
            align-items: center;
            transition: background-color var(--transition-speed) ease;
        }

        .chat-input-area input[type="text"] {
            flex-grow: 1;
            padding: 12px 15px;
            border: 1px solid var(--sidebar-border);
            border-radius: 25px;
            font-size: 1em;
            margin-right: 10px;
            outline: none;
            background-color: var(--input-bg);
            color: var(--text-color);
            transition: border-color var(--transition-speed) ease, background-color var(--transition-speed) ease;
        }

        .chat-input-area input[type="text"]:focus {
            border-color: #007bff;
        }

        .chat-input-area button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color var(--transition-speed) ease;
        }

        .chat-input-area button:hover {
            background-color: #0056b3;
        }

        /* Theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--button-bg);
            border: 1px solid var(--sidebar-border);
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 10px;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            z-index: 1000;
        }

        .theme-toggle:hover {
            background-color: var(--button-hover);
            transform: scale(1.1);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .sidebar {
                width: 250px;
                margin-right: 10px;
            }

            .chat-input-area {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
        <i class="fas fa-moon"></i>
    </button>

    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-robot"></i>
            Voyager AI
        </div>
        <ul class="sidebar-menu">
            <li><a href="#"><i class="fas fa-plus"></i> New Chat</a></li>
            <li><a href="#"><i class="fas fa-history"></i> Chat History</a></li>
            <li><a href="{{ url_for('profile') }}"><i class="fas fa-user"></i> Profile</a></li>
            <li><a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="chat-header">
            <span>Chat with Voyager AI</span>
            <div class="chat-actions">
                <button title="Clear Chat"><i class="fas fa-trash"></i></button>
                <button title="Settings"><i class="fas fa-cog"></i></button>
            </div>
        </div>

        <div class="chat-area" id="chat-area">
            <div class="message-container ai">
                <div class="message-bubble ai">
                    Hello! I'm Voyager AI, your cosmic companion. How can I help you explore the universe of knowledge today?
                </div>
            </div>
        </div>

        <div class="chat-input-area">
            <input type="text" id="user-input" placeholder="Type your message here..." autocomplete="off">
            <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
        </div>
    </div>

    <script>
        // Theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');

            if (root.classList.contains('light-theme')) {
                root.classList.remove('light-theme');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'dark');
            } else {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'light');
            }
        });

        // Load theme preference on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');

            if (savedTheme === 'light') {
                document.documentElement.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }
        });

        // Basic chat functionality
        function sendMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();

            if (message) {
                addMessage(message, 'user');
                input.value = '';

                // Simulate AI response (replace with actual API call)
                setTimeout(() => {
                    addMessage("I'm processing your request...", 'ai');
                }, 1000);
            }
        }

        function addMessage(text, sender) {
            const chatArea = document.getElementById('chat-area');
            const messageContainer = document.createElement('div');
            messageContainer.className = `message-container ${sender}`;

            const messageBubble = document.createElement('div');
            messageBubble.className = `message-bubble ${sender}`;
            messageBubble.textContent = text;

            messageContainer.appendChild(messageBubble);
            chatArea.appendChild(messageContainer);

            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // Enter key to send message
        document.getElementById('user-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
