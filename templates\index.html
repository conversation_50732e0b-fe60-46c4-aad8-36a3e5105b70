<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager AI - Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Dark theme (blue-black space) */
            --bg-color: #0a0a0f;
            --text-color: #e0e6ff;
            --primary-color: #4a90e2;
            --secondary-color: #2c5aa0;
            --accent-color: #6bb6ff;
            --tertiary-color: #1e3a8a;
            --button-bg: linear-gradient(135deg, #4a90e2, #2c5aa0);
            --button-hover: linear-gradient(135deg, #6bb6ff, #4a90e2);
            --container-bg: rgba(74, 144, 226, 0.1);
            --sidebar-bg: rgba(74, 144, 226, 0.15);
            --input-bg: rgba(74, 144, 226, 0.2);
            --input-container-bg: rgba(74, 144, 226, 0.15);
            --user-message-bg: rgba(74, 144, 226, 0.25);
            --bot-message-bg: rgba(44, 90, 160, 0.2);
            --bot-message-text: #e0e6ff;
            --chat-item-bg: rgba(74, 144, 226, 0.15);
            --chat-item-hover: rgba(74, 144, 226, 0.25);
            --user-info-bg: rgba(74, 144, 226, 0.2);
            --button-bg-solid: rgba(74, 144, 226, 0.25);
            --button-hover-solid: rgba(107, 182, 255, 0.3);
            --scrollbar-track: rgba(74, 144, 226, 0.1);
            --scrollbar-thumb: rgba(74, 144, 226, 0.4);
            --scrollbar-thumb-hover: rgba(107, 182, 255, 0.6);
            --modal-bg: rgba(74, 144, 226, 0.8);
            --active-chat-bg: rgba(74, 144, 226, 0.3);
            --active-chat-border: #4a90e2;
            --chat-menu-bg: rgba(74, 144, 226, 0.7);
            --chat-menu-hover: rgba(107, 182, 255, 0.5);
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --delete-btn: #ef5350;
            --cancel-btn: #9e9e9e;
            --saved-bg: rgba(74, 144, 226, 0.25);
            --star-color: #e0e6ff;
            --box-shadow: rgba(74, 144, 226, 0.2);
            --transition-speed: 0.3s;
            --nebula-color: rgba(74, 144, 226, 0.3);
            --nebula-secondary: rgba(44, 90, 160, 0.2);
            --nebula-tertiary: rgba(30, 58, 138, 0.15);
            --glow-color: rgba(74, 144, 226, 0.4);
            --sidebar-border: rgba(74, 144, 226, 0.25);
        }

        /* Light theme (light blue) */
        :root.light-theme {
            --bg-color: #f0f4ff;
            --text-color: #1e3a8a;
            --primary-color: #2563eb;
            --secondary-color: #3b82f6;
            --accent-color: #60a5fa;
            --tertiary-color: #1d4ed8;
            --button-bg: linear-gradient(135deg, #3b82f6, #60a5fa);
            --button-hover: linear-gradient(135deg, #2563eb, #3b82f6);
            --container-bg: rgba(37, 99, 235, 0.1);
            --sidebar-bg: rgba(37, 99, 235, 0.15);
            --input-bg: rgba(37, 99, 235, 0.2);
            --input-container-bg: rgba(37, 99, 235, 0.15);
            --user-message-bg: rgba(37, 99, 235, 0.25);
            --bot-message-bg: rgba(59, 130, 246, 0.2);
            --bot-message-text: #1e3a8a;
            --chat-item-bg: rgba(37, 99, 235, 0.15);
            --chat-item-hover: rgba(37, 99, 235, 0.25);
            --user-info-bg: rgba(37, 99, 235, 0.2);
            --button-bg-solid: rgba(37, 99, 235, 0.25);
            --button-hover-solid: rgba(96, 165, 250, 0.3);
            --scrollbar-track: rgba(37, 99, 235, 0.1);
            --scrollbar-thumb: rgba(37, 99, 235, 0.4);
            --scrollbar-thumb-hover: rgba(96, 165, 250, 0.6);
            --modal-bg: rgba(37, 99, 235, 0.8);
            --active-chat-bg: rgba(37, 99, 235, 0.3);
            --active-chat-border: #2563eb;
            --chat-menu-bg: rgba(37, 99, 235, 0.7);
            --chat-menu-hover: rgba(96, 165, 250, 0.5);
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --delete-btn: #ef5350;
            --cancel-btn: #9e9e9e;
            --saved-bg: rgba(37, 99, 235, 0.25);
            --star-color: #1e3a8a;
            --box-shadow: rgba(37, 99, 235, 0.15);
            --transition-speed: 0.3s;
            --nebula-color: rgba(96, 165, 250, 0.2);
            --nebula-secondary: rgba(59, 130, 246, 0.15);
            --nebula-tertiary: rgba(37, 99, 235, 0.1);
            --glow-color: rgba(37, 99, 235, 0.3);
            --sidebar-border: rgba(37, 99, 235, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: all var(--transition-speed) ease;
        }

        body {
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            padding: 20px;
            margin: 0;
            overflow: hidden;
            position: relative;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        /* Simplified space background */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(ellipse 600px 400px at 30% 40%, var(--nebula-color) 0%, transparent 60%),
                radial-gradient(ellipse 400px 300px at 70% 60%, var(--nebula-secondary) 0%, transparent 60%),
                var(--bg-color);
            z-index: -2;
            transition: background var(--transition-speed) ease;
        }

        /* Simplified Stars */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: var(--star-color);
            border-radius: 50%;
            animation: simpleTwinkle 4s ease-in-out infinite;
            transition: background-color var(--transition-speed) ease;
            opacity: 0.6;
        }

        .star.small {
            width: 1px;
            height: 1px;
        }

        .star.medium {
            width: 2px;
            height: 2px;
        }

        .star.large {
            width: 3px;
            height: 3px;
        }

        @keyframes simpleTwinkle {
            0%, 100% {
                opacity: 0.3;
            }
            50% {
                opacity: 0.8;
            }
        }

        .sidebar {
            width: 280px;
            min-width: 60px;
            background: var(--sidebar-bg);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            padding: 20px;
            box-shadow: 0 8px 25px var(--box-shadow);
            backdrop-filter: blur(10px);
            border: 1px solid var(--sidebar-border);
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
            z-index: 100;
            transition: all var(--transition-speed) ease;
            border-radius: 15px;
            margin-right: 20px;
        }

        .sidebar.collapsed {
            width: 70px;
            padding: 20px 10px;
        }

        .sidebar-toggle {
            position: absolute;
            top: 15px;
            right: -12px;
            background: var(--container-bg);
            border: 1px solid var(--primary-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 14px;
            padding: 6px;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            z-index: 101;
            box-shadow: 0 4px 15px var(--box-shadow);
            backdrop-filter: blur(10px);
            opacity: 0;
        }

        .sidebar:hover .sidebar-toggle {
            opacity: 1;
        }

        .sidebar-toggle:hover {
            transform: scale(1.1);
            background: var(--button-bg-solid);
        }

        .sidebar-header {
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.3em;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .sidebar-header {
            font-size: 0;
        }

        .sidebar-header i {
            margin-right: 8px;
            font-size: 1.1em;
        }

        .sidebar.collapsed .sidebar-header i {
            margin-right: 0;
            font-size: 1.3em;
        }

        .new-chat-btn {
            background: var(--button-bg);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 15px var(--box-shadow);
            border: 1px solid var(--primary-color);
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .new-chat-btn {
            padding: 10px;
            font-size: 0;
            gap: 0;
        }

        .sidebar.collapsed .new-chat-btn i {
            margin: 0;
        }

        .new-chat-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px var(--box-shadow);
            background: var(--button-hover);
        }

        .chat-history {
            flex-grow: 1;
            margin-bottom: 20px;
        }

        .chat-history h3 {
            font-size: 14px;
            color: var(--text-color);
            margin-bottom: 15px;
            opacity: 0.8;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            transition: all var(--transition-speed) ease;
        }

        .sidebar.collapsed .chat-history h3 {
            font-size: 0;
        }

        .chat-item {
            background: var(--chat-item-bg);
            border-radius: 10px;
            padding: 10px 12px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            border: 1px solid var(--sidebar-border);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px var(--box-shadow);
            overflow: hidden;
        }

        .chat-item:hover {
            background: var(--chat-item-hover);
            transform: translateX(3px);
        }

        .chat-item.active {
            background: var(--active-chat-bg);
            border-color: var(--active-chat-border);
        }

        .chat-item-content {
            flex-grow: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 14px;
            transition: all var(--transition-speed) ease;
        }

        .sidebar.collapsed .chat-item-content {
            font-size: 0;
        }

        .chat-item-number {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 10px;
            flex-shrink: 0;
            box-shadow: 0 0 10px var(--glow-color);
        }

        .sidebar.collapsed .chat-item-number {
            margin-right: 0;
        }

        .chat-menu-btn {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all var(--transition-speed) ease;
            opacity: 0;
            font-size: 14px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-item:hover .chat-menu-btn {
            opacity: 1;
        }

        .sidebar.collapsed .chat-menu-btn {
            opacity: 0 !important;
        }

        .chat-menu-btn:hover {
            background: var(--chat-menu-hover);
            transform: scale(1.1);
        }

        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--container-bg);
            border-radius: 15px;
            overflow: hidden;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 8px 25px var(--box-shadow);
            backdrop-filter: blur(10px);
            border: 1px solid var(--sidebar-border);
            position: relative;
        }

        .chat-header {
            background: var(--input-container-bg);
            color: var(--primary-color);
            padding: 16px 20px;
            font-size: 1.2em;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px var(--box-shadow);
            z-index: 50;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--sidebar-border);
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .chat-actions button {
            background: var(--button-bg-solid);
            border: 1px solid var(--sidebar-border);
            color: var(--text-color);
            cursor: pointer;
            font-size: 16px;
            padding: 8px 12px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px var(--box-shadow);
        }

        .chat-actions button:hover {
            background: var(--button-hover-solid);
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 6px 20px var(--glow-color);
        }

        .chat-area {
            flex-grow: 1;
            padding: 25px;
            overflow-y: auto;
            background: transparent;
            display: flex;
            flex-direction: column;
            transition: all var(--transition-speed) ease;
            position: relative;
            z-index: 1;
        }

        .chat-area::-webkit-scrollbar {
            width: 8px;
        }

        .chat-area::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: 10px;
        }

        .chat-area::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 10px;
            transition: background var(--transition-speed) ease;
        }

        .chat-area::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }

        .message-container {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }

        .message-container.user {
            justify-content: flex-end;
        }

        .message-container.ai {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
            box-shadow: 0 4px 12px var(--box-shadow);
            backdrop-filter: blur(10px);
            border: 1px solid var(--sidebar-border);
            transition: all var(--transition-speed) ease;
        }

        .message-bubble.user {
            background: var(--user-message-bg);
            color: var(--text-color);
            border-bottom-right-radius: 6px;
        }

        .message-bubble.ai {
            background: var(--bot-message-bg);
            color: var(--bot-message-text);
            border-bottom-left-radius: 6px;
        }

        .chat-input-area {
            padding: 20px 25px;
            background: var(--input-container-bg);
            border-top: 1px solid var(--sidebar-border);
            display: flex;
            align-items: center;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 -4px 20px var(--box-shadow);
        }

        .chat-input-area input[type="text"] {
            flex-grow: 1;
            padding: 15px 20px;
            border: 2px solid var(--sidebar-border);
            border-radius: 30px;
            font-size: 16px;
            margin-right: 15px;
            outline: none;
            background: var(--input-bg);
            color: var(--text-color);
            transition: all var(--transition-speed) ease;
            box-shadow: 0 8px 25px var(--box-shadow), inset 0 0 15px var(--glow-color);
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .chat-input-area input[type="text"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 12px 35px var(--glow-color), inset 0 0 25px var(--glow-color);
            transform: translateY(-2px);
        }

        .chat-input-area input[type="text"]::placeholder {
            color: var(--text-color);
            opacity: 0.6;
        }

        .chat-input-area button {
            background: var(--button-bg);
            color: white;
            border: 2px solid var(--primary-color);
            padding: 15px 20px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 8px 25px var(--glow-color), inset 0 0 15px rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }

        .chat-input-area button:hover {
            background: var(--button-hover);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px var(--glow-color), inset 0 0 25px rgba(255, 255, 255, 0.2);
        }

        /* Simplified theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--container-bg);
            border: 1px solid var(--primary-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 18px;
            padding: 10px;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            z-index: 1000;
            box-shadow: 0 4px 15px var(--box-shadow);
            backdrop-filter: blur(10px);
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            background: var(--button-bg-solid);
            color: white;
        }

        /* Chat menu dropdown */
        .chat-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--chat-menu-bg);
            border: 1px solid var(--sidebar-border);
            border-radius: 10px;
            padding: 8px 0;
            min-width: 120px;
            box-shadow: 0 8px 25px var(--box-shadow);
            backdrop-filter: blur(15px);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all var(--transition-speed) ease;
        }

        .chat-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .chat-menu button {
            width: 100%;
            background: none;
            border: none;
            color: var(--text-color);
            padding: 10px 15px;
            text-align: left;
            cursor: pointer;
            transition: background-color var(--transition-speed) ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-menu button:hover {
            background: var(--chat-menu-hover);
        }

        .chat-menu button.delete {
            color: var(--delete-btn);
        }

        /* User menu at bottom of sidebar */
        .user-menu {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid var(--sidebar-border);
        }

        .user-menu a {
            color: var(--text-color);
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 12px;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .user-menu a {
            padding: 10px;
            justify-content: center;
        }

        .sidebar.collapsed .user-menu a span {
            display: none;
        }

        .user-menu a:hover {
            background: var(--chat-item-hover);
            transform: translateX(5px);
        }

        .user-menu a i {
            width: 16px;
            text-align: center;
            flex-shrink: 0;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                margin-right: 0;
                margin-bottom: 10px;
                max-height: 200px;
            }

            .sidebar.collapsed {
                width: 100%;
                max-height: 60px;
            }

            .main-content {
                flex-grow: 1;
            }

            .chat-input-area {
                padding: 15px 20px;
            }

            .theme-toggle {
                top: 10px;
                right: 10px;
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                padding: 15px;
            }

            .chat-area {
                padding: 15px;
            }

            .message-bubble {
                max-width: 85%;
                padding: 12px 16px;
            }

            .chat-input-area input[type="text"] {
                padding: 12px 16px;
                font-size: 14px;
            }

            .chat-input-area button {
                padding: 12px 16px;
                font-size: 14px;
                min-width: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="space-background"></div>
    <div class="stars" id="stars"></div>

    <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
        <i class="fas fa-moon"></i>
    </button>

    <div class="sidebar" id="sidebar">
        <button class="sidebar-toggle" id="sidebar-toggle" title="Toggle Sidebar">
            <i class="fas fa-chevron-left"></i>
        </button>

        <div class="sidebar-header">
            <i class="fas fa-rocket"></i>
            <span>Voyager AI</span>
        </div>

        <button class="new-chat-btn" onclick="startNewChat()">
            <i class="fas fa-plus"></i>
            <span>New Chat</span>
        </button>

        <div class="chat-history">
            <h3>Recent Chats</h3>
            <div id="chat-list">
                <!-- Chat items will be dynamically added here -->
                <div class="chat-item active" data-chat-id="1">
                    <div class="chat-item-number">1</div>
                    <div class="chat-item-content">Welcome Chat</div>
                    <button class="chat-menu-btn" onclick="toggleChatMenu(event, 1)">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="chat-menu" id="chat-menu-1">
                        <button onclick="renameChat(1)">
                            <i class="fas fa-edit"></i> Rename
                        </button>
                        <button onclick="deleteChat(1)" class="delete">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-menu">
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <div class="main-content">
        <div class="chat-header">
            <span>Chat with Voyager AI</span>
            <div class="chat-actions">
                <button title="Clear Chat"><i class="fas fa-trash"></i></button>
                <button title="Settings"><i class="fas fa-cog"></i></button>
            </div>
        </div>

        <div class="chat-area" id="chat-area">
            <!-- Messages will be added here dynamically -->
        </div>

        <div class="chat-input-area">
            <input type="text" id="user-input" placeholder="Type your message here..." autocomplete="off">
            <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
        </div>
    </div>

    <script>
        // Create simple cosmic stars
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const starCount = 80;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random size
                const sizeRandom = Math.random();
                let size;
                if (sizeRandom < 0.8) size = 'small';
                else if (sizeRandom < 0.95) size = 'medium';
                else size = 'large';

                star.classList.add(size);

                // Random position
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // Random animation delay
                star.style.animationDelay = Math.random() * 4 + 's';

                starsContainer.appendChild(star);
            }
        }

        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const toggleIcon = document.querySelector('.sidebar-toggle i');

            sidebar.classList.toggle('collapsed');

            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.classList.remove('fa-chevron-left');
                toggleIcon.classList.add('fa-chevron-right');
            } else {
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-left');
            }
        }

        // Theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');

            if (root.classList.contains('light-theme')) {
                // Switch to dark theme
                root.classList.remove('light-theme');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'dark');
            } else {
                // Switch to light theme
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'light');
            }
        });

        // Chat management functions
        let chatCounter = 1;
        let currentChatId = 1;

        function startNewChat() {
            chatCounter++;
            const chatList = document.getElementById('chat-list');

            // Remove active class from all chats
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('active');
            });

            // Create new chat item
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-item active';
            chatItem.setAttribute('data-chat-id', chatCounter);
            chatItem.innerHTML = `
                <div class="chat-item-number">${chatCounter}</div>
                <div class="chat-item-content">New Chat ${chatCounter}</div>
                <button class="chat-menu-btn" onclick="toggleChatMenu(event, ${chatCounter})">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="chat-menu" id="chat-menu-${chatCounter}">
                    <button onclick="renameChat(${chatCounter})">
                        <i class="fas fa-edit"></i> Rename
                    </button>
                    <button onclick="deleteChat(${chatCounter})" class="delete">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            `;

            chatList.insertBefore(chatItem, chatList.firstChild);
            currentChatId = chatCounter;

            // Clear chat area
            document.getElementById('chat-area').innerHTML = `
                <div class="message-container ai">
                    <div class="message-bubble ai">
                        Hello! I'm Voyager AI, your cosmic companion. How can I help you explore the universe of knowledge today?
                    </div>
                </div>
            `;
        }

        function toggleChatMenu(event, chatId) {
            event.stopPropagation();
            const menu = document.getElementById(`chat-menu-${chatId}`);

            // Close all other menus
            document.querySelectorAll('.chat-menu').forEach(m => {
                if (m !== menu) m.classList.remove('show');
            });

            menu.classList.toggle('show');
        }

        function renameChat(chatId) {
            const chatItem = document.querySelector(`[data-chat-id="${chatId}"] .chat-item-content`);
            const currentName = chatItem.textContent;
            const newName = prompt('Enter new chat name:', currentName);

            if (newName && newName.trim()) {
                chatItem.textContent = newName.trim();
            }

            // Close menu
            document.getElementById(`chat-menu-${chatId}`).classList.remove('show');
        }

        function deleteChat(chatId) {
            if (confirm('Are you sure you want to delete this chat?')) {
                const chatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
                chatItem.remove();

                // If deleted chat was active, activate the first remaining chat
                if (currentChatId === chatId) {
                    const firstChat = document.querySelector('.chat-item');
                    if (firstChat) {
                        firstChat.classList.add('active');
                        currentChatId = parseInt(firstChat.getAttribute('data-chat-id'));
                    }
                }
            }
        }

        // Basic chat functionality
        function sendMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();

            if (message) {
                addMessage(message, 'user');
                input.value = '';

                // Simulate AI response (replace with actual API call)
                setTimeout(() => {
                    addMessage("I'm processing your request and exploring the cosmic database for answers...", 'ai');
                }, 1000);
            }
        }

        function addMessage(text, sender) {
            const chatArea = document.getElementById('chat-area');
            const messageContainer = document.createElement('div');
            messageContainer.className = `message-container ${sender}`;

            const messageBubble = document.createElement('div');
            messageBubble.className = `message-bubble ${sender}`;
            messageBubble.textContent = text;

            messageContainer.appendChild(messageBubble);
            chatArea.appendChild(messageContainer);

            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // Event listeners
        document.getElementById('sidebar-toggle').addEventListener('click', toggleSidebar);
        document.getElementById('user-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Close chat menus when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.chat-menu') && !event.target.closest('.chat-menu-btn')) {
                document.querySelectorAll('.chat-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // Load theme preference and initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const root = document.documentElement;

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // Initialize animations
            createStars();
        });
    </script>
</body>
</html>
