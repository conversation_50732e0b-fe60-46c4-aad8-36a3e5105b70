﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager AI - Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Dark theme (nebula purple focused) */
            --bg-color: #0a0a0f;
            --text-color: #e8e3ff;
            --primary-color: #7b68ee;
            --secondary-color: #9370db;
            --accent-color: #da70d6;
            --tertiary-color: #8a2be2;
            --button-bg: linear-gradient(135deg, #7b68ee, #9370db);
            --button-hover: linear-gradient(135deg, #9370db, #8a2be2);
            --container-bg: rgba(75, 0, 130, 0.15);
            --sidebar-bg: rgba(75, 0, 130, 0.2);
            --input-bg: rgba(75, 0, 130, 0.25);
            --input-container-bg: rgba(75, 0, 130, 0.2);
            --user-message-bg: rgba(123, 104, 238, 0.3);
            --bot-message-bg: rgba(147, 112, 219, 0.2);
            --bot-message-text: #e8e3ff;
            --chat-item-bg: rgba(75, 0, 130, 0.2);
            --chat-item-hover: rgba(123, 104, 238, 0.3);
            --user-info-bg: rgba(75, 0, 130, 0.25);
            --button-bg-solid: rgba(123, 104, 238, 0.3);
            --button-hover-solid: rgba(147, 112, 219, 0.4);
            --scrollbar-track: rgba(75, 0, 130, 0.1);
            --scrollbar-thumb: rgba(123, 104, 238, 0.5);
            --scrollbar-thumb-hover: rgba(147, 112, 219, 0.7);
            --modal-bg: rgba(75, 0, 130, 0.9);
            --active-chat-bg: rgba(123, 104, 238, 0.4);
            --active-chat-border: #7b68ee;
            --chat-menu-bg: rgba(75, 0, 130, 0.8);
            --chat-menu-hover: rgba(123, 104, 238, 0.6);
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --delete-btn: #ef5350;
            --cancel-btn: #9e9e9e;
            --saved-bg: rgba(75, 0, 130, 0.3);
            --star-color: #ffd700;
            --box-shadow: rgba(123, 104, 238, 0.3);
            --transition-speed: 0.4s;
            --nebula-color: rgba(123, 104, 238, 0.4);
            --nebula-secondary: rgba(147, 112, 219, 0.3);
            --nebula-tertiary: rgba(138, 43, 226, 0.2);
            --glow-color: rgba(123, 104, 238, 0.6);
            --sidebar-border: rgba(123, 104, 238, 0.3);
        }

        /* Light theme (cosmic lavender) */
        :root.light-theme {
            --bg-color: #f8f6ff;
            --text-color: #4a148c;
            --primary-color: #8e24aa;
            --secondary-color: #ab47bc;
            --accent-color: #ce93d8;
            --tertiary-color: #7b1fa2;
            --button-bg: linear-gradient(135deg, #ab47bc, #ce93d8);
            --button-hover: linear-gradient(135deg, #8e24aa, #ab47bc);
            --container-bg: rgba(206, 147, 216, 0.2);
            --sidebar-bg: rgba(206, 147, 216, 0.25);
            --input-bg: rgba(206, 147, 216, 0.3);
            --input-container-bg: rgba(206, 147, 216, 0.25);
            --user-message-bg: rgba(142, 36, 170, 0.3);
            --bot-message-bg: rgba(171, 71, 188, 0.2);
            --bot-message-text: #4a148c;
            --chat-item-bg: rgba(206, 147, 216, 0.2);
            --chat-item-hover: rgba(142, 36, 170, 0.3);
            --user-info-bg: rgba(206, 147, 216, 0.3);
            --button-bg-solid: rgba(142, 36, 170, 0.3);
            --button-hover-solid: rgba(171, 71, 188, 0.4);
            --scrollbar-track: rgba(206, 147, 216, 0.1);
            --scrollbar-thumb: rgba(142, 36, 170, 0.5);
            --scrollbar-thumb-hover: rgba(171, 71, 188, 0.7);
            --modal-bg: rgba(206, 147, 216, 0.9);
            --active-chat-bg: rgba(142, 36, 170, 0.4);
            --active-chat-border: #8e24aa;
            --chat-menu-bg: rgba(206, 147, 216, 0.8);
            --chat-menu-hover: rgba(142, 36, 170, 0.6);
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --delete-btn: #ef5350;
            --cancel-btn: #9e9e9e;
            --saved-bg: rgba(206, 147, 216, 0.3);
            --star-color: #ffc107;
            --box-shadow: rgba(142, 36, 170, 0.2);
            --transition-speed: 0.4s;
            --nebula-color: rgba(206, 147, 216, 0.3);
            --nebula-secondary: rgba(171, 71, 188, 0.2);
            --nebula-tertiary: rgba(142, 36, 170, 0.15);
            --glow-color: rgba(142, 36, 170, 0.4);
            --sidebar-border: rgba(142, 36, 170, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: all var(--transition-speed) ease;
        }

        body {
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            padding: 20px;
            margin: 0;
            overflow: hidden;
            position: relative;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
            opacity: 0;
            animation: cosmicFadeIn 1.5s ease-out forwards;
        }

        @keyframes cosmicFadeIn {
            from {
                opacity: 0;
                transform: scale(1.02);
                filter: blur(1px);
            }
            to {
                opacity: 1;
                transform: scale(1);
                filter: blur(0px);
            }
        }

        /* Enhanced space background with multiple nebula layers */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(ellipse 800px 600px at 20% 30%, var(--nebula-color) 0%, transparent 50%),
                radial-gradient(ellipse 600px 400px at 80% 70%, var(--nebula-secondary) 0%, transparent 50%),
                radial-gradient(ellipse 400px 300px at 50% 20%, var(--nebula-tertiary) 0%, transparent 50%),
                var(--bg-color);
            z-index: -2;
            transition: background var(--transition-speed) ease;
            animation: nebulaFloat 20s ease-in-out infinite;
        }

        @keyframes nebulaFloat {
            0%, 100% {
                filter: hue-rotate(0deg) brightness(1);
            }
            33% {
                filter: hue-rotate(10deg) brightness(1.1);
            }
            66% {
                filter: hue-rotate(-10deg) brightness(0.9);
            }
        }

        /* Stars */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: var(--text-color);
            border-radius: 50%;
            animation: cosmicTwinkle 3s ease-in-out infinite;
            transition: background-color var(--transition-speed) ease;
            box-shadow: 0 0 6px var(--glow-color);
        }

        .star.small {
            width: 1px;
            height: 1px;
            animation-duration: 2.5s;
        }

        .star.medium {
            width: 2px;
            height: 2px;
            animation-duration: 3.5s;
            box-shadow: 0 0 8px var(--glow-color);
        }

        .star.large {
            width: 3px;
            height: 3px;
            animation-duration: 4s;
            box-shadow: 0 0 12px var(--glow-color);
        }

        @keyframes cosmicTwinkle {
            0%, 100% {
                opacity: 0.4;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        .sidebar {
            width: 320px;
            min-width: 60px;
            background: var(--sidebar-bg);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            padding: 20px;
            box-shadow: 0 25px 50px var(--box-shadow), inset 0 0 30px var(--glow-color);
            backdrop-filter: blur(15px);
            border: 2px solid var(--sidebar-border);
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
            z-index: 100;
            transition: all var(--transition-speed) ease;
            border-radius: 25px;
            margin-right: 20px;
            animation: gentleFloat 10s ease-in-out infinite;
        }

        .sidebar.collapsed {
            width: 80px;
            padding: 20px 10px;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, var(--glow-color), transparent);
            animation: gentleSweep 15s linear infinite;
            opacity: 0.05;
        }

        @keyframes gentleFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-3px);
            }
        }

        @keyframes gentleSweep {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .sidebar-toggle {
            position: absolute;
            top: 15px;
            right: -15px;
            background: var(--container-bg);
            border: 2px solid var(--primary-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 16px;
            padding: 8px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            z-index: 101;
            box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 15px var(--glow-color);
            backdrop-filter: blur(10px);
            opacity: 0;
        }

        .sidebar:hover .sidebar-toggle {
            opacity: 1;
        }

        .sidebar-toggle:hover {
            transform: scale(1.1);
            background: var(--button-bg);
            color: white;
            box-shadow: 0 6px 25px var(--glow-color), inset 0 0 20px rgba(255, 255, 255, 0.2);
        }

        .sidebar-header {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.5em;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all var(--transition-speed) ease;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .sidebar-header {
            font-size: 0;
        }

        .sidebar-header i {
            margin-right: 10px;
            color: var(--primary-color);
            filter: drop-shadow(0 0 8px var(--glow-color));
            font-size: 1.2em;
        }

        .sidebar.collapsed .sidebar-header i {
            margin-right: 0;
            font-size: 1.5em;
        }

        .new-chat-btn {
            background: var(--button-bg);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 6px 25px var(--glow-color), inset 0 0 20px rgba(255, 255, 255, 0.1);
            border: 2px solid var(--primary-color);
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .new-chat-btn {
            padding: 12px;
            font-size: 0;
            gap: 0;
        }

        .sidebar.collapsed .new-chat-btn i {
            margin: 0;
        }

        .new-chat-btn:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 12px 40px var(--glow-color), inset 0 0 30px rgba(255, 255, 255, 0.2);
            background: var(--button-hover);
        }

        .chat-history {
            flex-grow: 1;
            margin-bottom: 20px;
        }

        .chat-history h3 {
            font-size: 14px;
            color: var(--text-color);
            margin-bottom: 15px;
            opacity: 0.8;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            transition: all var(--transition-speed) ease;
        }

        .sidebar.collapsed .chat-history h3 {
            font-size: 0;
        }

        .chat-item {
            background: var(--chat-item-bg);
            border-radius: 15px;
            padding: 12px 15px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            border: 1px solid var(--sidebar-border);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px var(--box-shadow);
            overflow: hidden;
        }

        .chat-item:hover {
            background: var(--chat-item-hover);
            transform: translateX(5px);
            box-shadow: 0 6px 20px var(--glow-color);
        }

        .chat-item.active {
            background: var(--active-chat-bg);
            border-color: var(--active-chat-border);
            box-shadow: 0 6px 25px var(--glow-color);
        }

        .chat-item-content {
            flex-grow: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 14px;
            transition: all var(--transition-speed) ease;
        }

        .sidebar.collapsed .chat-item-content {
            font-size: 0;
        }

        .chat-item-number {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 10px;
            flex-shrink: 0;
            box-shadow: 0 0 10px var(--glow-color);
        }

        .sidebar.collapsed .chat-item-number {
            margin-right: 0;
        }

        .chat-menu-btn {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all var(--transition-speed) ease;
            opacity: 0;
            font-size: 14px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-item:hover .chat-menu-btn {
            opacity: 1;
        }

        .sidebar.collapsed .chat-menu-btn {
            opacity: 0 !important;
        }

        .chat-menu-btn:hover {
            background: var(--chat-menu-hover);
            transform: scale(1.1);
        }

        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--container-bg);
            border-radius: 25px;
            overflow: hidden;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 25px 50px var(--box-shadow), inset 0 0 30px var(--glow-color);
            backdrop-filter: blur(15px);
            border: 2px solid var(--sidebar-border);
            position: relative;
            animation: gentleFloat 8s ease-in-out infinite;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, var(--glow-color), transparent);
            animation: gentleSweep 12s linear infinite;
            opacity: 0.05;
        }

        .chat-header {
            background: var(--input-container-bg);
            color: var(--text-color);
            padding: 20px 25px;
            font-size: 1.3em;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px var(--box-shadow);
            z-index: 50;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--sidebar-border);
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .chat-actions button {
            background: var(--button-bg-solid);
            border: 1px solid var(--sidebar-border);
            color: var(--text-color);
            cursor: pointer;
            font-size: 16px;
            padding: 8px 12px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px var(--box-shadow);
        }

        .chat-actions button:hover {
            background: var(--button-hover-solid);
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 6px 20px var(--glow-color);
        }

        .chat-area {
            flex-grow: 1;
            padding: 25px;
            overflow-y: auto;
            background: transparent;
            display: flex;
            flex-direction: column;
            transition: all var(--transition-speed) ease;
            position: relative;
            z-index: 1;
        }

        .chat-area::-webkit-scrollbar {
            width: 8px;
        }

        .chat-area::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: 10px;
        }

        .chat-area::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 10px;
            transition: background var(--transition-speed) ease;
        }

        .chat-area::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }

        .message-container {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-start;
            animation: messageSlideIn 0.5s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-container.user {
            justify-content: flex-end;
        }

        .message-container.ai {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 25px;
            line-height: 1.6;
            word-wrap: break-word;
            box-shadow: 0 8px 25px var(--box-shadow), inset 0 0 15px var(--glow-color);
            backdrop-filter: blur(10px);
            border: 1px solid var(--sidebar-border);
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .message-bubble::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, var(--glow-color), transparent);
            animation: gentleSweep 20s linear infinite;
            opacity: 0.03;
        }

        .message-bubble.user {
            background: var(--user-message-bg);
            color: var(--text-color);
            border-bottom-right-radius: 8px;
        }

        .message-bubble.ai {
            background: var(--bot-message-bg);
            color: var(--bot-message-text);
            border-bottom-left-radius: 8px;
        }

        .message-bubble:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px var(--glow-color), inset 0 0 25px var(--glow-color);
        }

        .chat-input-area {
            padding: 20px 25px;
            background: var(--input-container-bg);
            border-top: 1px solid var(--sidebar-border);
            display: flex;
            align-items: center;
            transition: all var(--transition-speed) ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 -4px 20px var(--box-shadow);
        }

        .chat-input-area input[type="text"] {
            flex-grow: 1;
            padding: 15px 20px;
            border: 2px solid var(--sidebar-border);
            border-radius: 30px;
            font-size: 16px;
            margin-right: 15px;
            outline: none;
            background: var(--input-bg);
            color: var(--text-color);
            transition: all var(--transition-speed) ease;
            box-shadow: 0 8px 25px var(--box-shadow), inset 0 0 15px var(--glow-color);
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .chat-input-area input[type="text"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 12px 35px var(--glow-color), inset 0 0 25px var(--glow-color);
            transform: translateY(-2px);
        }

        .chat-input-area input[type="text"]::placeholder {
            color: var(--text-color);
            opacity: 0.6;
        }

        .chat-input-area button {
            background: var(--button-bg);
            color: white;
            border: 2px solid var(--primary-color);
            padding: 15px 20px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 8px 25px var(--glow-color), inset 0 0 15px rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }

        .chat-input-area button:hover {
            background: var(--button-hover);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px var(--glow-color), inset 0 0 25px rgba(255, 255, 255, 0.2);
        }

        /* Enhanced theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--container-bg);
            border: 2px solid var(--primary-color);
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 12px;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease, transform 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 20px var(--glow-color);
            backdrop-filter: blur(10px);
            animation: cosmicPulse 4s ease-in-out infinite;
        }

        @keyframes cosmicPulse {
            0%, 100% {
                box-shadow: 0 4px 20px var(--box-shadow), inset 0 0 20px var(--glow-color);
            }
            50% {
                box-shadow: 0 6px 30px var(--box-shadow), inset 0 0 30px var(--glow-color);
            }
        }

        .theme-toggle:hover {
            transform: scale(1.15) rotate(180deg);
            background: var(--button-bg);
            color: white;
            box-shadow: 0 8px 40px var(--glow-color), inset 0 0 40px rgba(255, 255, 255, 0.2);
            animation: none;
        }

        .theme-toggle i {
            transition: transform var(--transition-speed) ease;
            filter: drop-shadow(0 0 8px var(--glow-color));
        }

        /* Chat menu dropdown */
        .chat-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--chat-menu-bg);
            border: 1px solid var(--sidebar-border);
            border-radius: 10px;
            padding: 8px 0;
            min-width: 120px;
            box-shadow: 0 8px 25px var(--box-shadow);
            backdrop-filter: blur(15px);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all var(--transition-speed) ease;
        }

        .chat-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .chat-menu button {
            width: 100%;
            background: none;
            border: none;
            color: var(--text-color);
            padding: 10px 15px;
            text-align: left;
            cursor: pointer;
            transition: background-color var(--transition-speed) ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-menu button:hover {
            background: var(--chat-menu-hover);
        }

        .chat-menu button.delete {
            color: var(--delete-btn);
        }

        /* User menu at bottom of sidebar */
        .user-menu {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid var(--sidebar-border);
        }

        .user-menu a {
            color: var(--text-color);
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 12px;
            transition: all var(--transition-speed) ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .user-menu a {
            padding: 10px;
            justify-content: center;
        }

        .sidebar.collapsed .user-menu a span {
            display: none;
        }

        .user-menu a:hover {
            background: var(--chat-item-hover);
            transform: translateX(5px);
        }

        .user-menu a i {
            width: 16px;
            text-align: center;
            flex-shrink: 0;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                margin-right: 0;
                margin-bottom: 10px;
                max-height: 200px;
            }

            .sidebar.collapsed {
                width: 100%;
                max-height: 60px;
            }

            .main-content {
                flex-grow: 1;
            }

            .chat-input-area {
                padding: 15px 20px;
            }

            .theme-toggle {
                top: 10px;
                right: 10px;
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                padding: 15px;
            }

            .chat-area {
                padding: 15px;
            }

            .message-bubble {
                max-width: 85%;
                padding: 12px 16px;
            }

            .chat-input-area input[type="text"] {
                padding: 12px 16px;
                font-size: 14px;
            }

            .chat-input-area button {
                padding: 12px 16px;
                font-size: 14px;
                min-width: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="space-background"></div>
    <div class="stars" id="stars"></div>

    <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
        <i class="fas fa-moon"></i>
    </button>

    <div class="sidebar" id="sidebar">
        <button class="sidebar-toggle" id="sidebar-toggle" title="Toggle Sidebar">
            <i class="fas fa-chevron-left"></i>
        </button>

        <div class="sidebar-header">
            <i class="fas fa-rocket"></i>
            <span>Voyager AI</span>
        </div>

        <button class="new-chat-btn" onclick="startNewChat()">
            <i class="fas fa-plus"></i>
            <span>New Chat</span>
        </button>

        <div class="chat-history">
            <h3>Recent Chats</h3>
            <div id="chat-list">
                <!-- Chat items will be dynamically added here -->
                <div class="chat-item active" data-chat-id="1">
                    <div class="chat-item-number">1</div>
                    <div class="chat-item-content">Welcome Chat</div>
                    <button class="chat-menu-btn" onclick="toggleChatMenu(event, 1)">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="chat-menu" id="chat-menu-1">
                        <button onclick="renameChat(1)">
                            <i class="fas fa-edit"></i> Rename
                        </button>
                        <button onclick="deleteChat(1)" class="delete">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-menu">
            <a href="{{ url_for('profile') }}">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
            <a href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <div class="main-content">
        <div class="chat-header">
            <span>Chat with Voyager AI</span>
            <div class="chat-actions">
                <button title="Clear Chat"><i class="fas fa-trash"></i></button>
                <button title="Settings"><i class="fas fa-cog"></i></button>
            </div>
        </div>

        <div class="chat-area" id="chat-area">
            <div class="message-container ai">
                <div class="message-bubble ai">
                    Hello! I'm Voyager AI, your cosmic companion. How can I help you explore the universe of knowledge today?
                </div>
            </div>
        </div>

        <div class="chat-input-area">
            <input type="text" id="user-input" placeholder="Type your message here..." autocomplete="off">
            <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
        </div>
    </div>

    <script>
        // Create enhanced cosmic stars
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const starCount = 150;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random size with weighted distribution
                const sizeRandom = Math.random();
                let size;
                if (sizeRandom < 0.7) size = 'small';
                else if (sizeRandom < 0.9) size = 'medium';
                else size = 'large';

                star.classList.add(size);

                // Random position
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // Random animation delay and duration
                star.style.animationDelay = Math.random() * 4 + 's';
                star.style.animationDuration = (2.5 + Math.random() * 2) + 's';

                starsContainer.appendChild(star);
            }
        }

        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const toggleIcon = document.querySelector('.sidebar-toggle i');

            sidebar.classList.toggle('collapsed');

            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.classList.remove('fa-chevron-left');
                toggleIcon.classList.add('fa-chevron-right');
            } else {
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-left');
            }
        }

        // Enhanced theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');
            const button = this;

            // Add cosmic transition effect
            button.style.transform = 'scale(0.8) rotate(360deg)';
            button.style.filter = 'brightness(1.5)';

            setTimeout(() => {
                if (root.classList.contains('light-theme')) {
                    // Switch to dark theme
                    root.classList.remove('light-theme');
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                    localStorage.setItem('theme', 'dark');
                } else {
                    // Switch to light theme
                    root.classList.add('light-theme');
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                    localStorage.setItem('theme', 'light');
                }

                // Reset button transform
                setTimeout(() => {
                    button.style.transform = '';
                    button.style.filter = '';
                }, 200);
            }, 200);
        });

        // Chat management functions
        let chatCounter = 1;
        let currentChatId = 1;

        function startNewChat() {
            chatCounter++;
            const chatList = document.getElementById('chat-list');

            // Remove active class from all chats
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('active');
            });

            // Create new chat item
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-item active';
            chatItem.setAttribute('data-chat-id', chatCounter);
            chatItem.innerHTML = `
                <div class="chat-item-number">${chatCounter}</div>
                <div class="chat-item-content">New Chat ${chatCounter}</div>
                <button class="chat-menu-btn" onclick="toggleChatMenu(event, ${chatCounter})">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="chat-menu" id="chat-menu-${chatCounter}">
                    <button onclick="renameChat(${chatCounter})">
                        <i class="fas fa-edit"></i> Rename
                    </button>
                    <button onclick="deleteChat(${chatCounter})" class="delete">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            `;

            chatList.insertBefore(chatItem, chatList.firstChild);
            currentChatId = chatCounter;

            // Clear chat area
            document.getElementById('chat-area').innerHTML = `
                <div class="message-container ai">
                    <div class="message-bubble ai">
                        Hello! I'm Voyager AI, your cosmic companion. How can I help you explore the universe of knowledge today?
                    </div>
                </div>
            `;
        }

        function toggleChatMenu(event, chatId) {
            event.stopPropagation();
            const menu = document.getElementById(`chat-menu-${chatId}`);

            // Close all other menus
            document.querySelectorAll('.chat-menu').forEach(m => {
                if (m !== menu) m.classList.remove('show');
            });

            menu.classList.toggle('show');
        }

        function renameChat(chatId) {
            const chatItem = document.querySelector(`[data-chat-id="${chatId}"] .chat-item-content`);
            const currentName = chatItem.textContent;
            const newName = prompt('Enter new chat name:', currentName);

            if (newName && newName.trim()) {
                chatItem.textContent = newName.trim();
            }

            // Close menu
            document.getElementById(`chat-menu-${chatId}`).classList.remove('show');
        }

        function deleteChat(chatId) {
            if (confirm('Are you sure you want to delete this chat?')) {
                const chatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
                chatItem.remove();

                // If deleted chat was active, activate the first remaining chat
                if (currentChatId === chatId) {
                    const firstChat = document.querySelector('.chat-item');
                    if (firstChat) {
                        firstChat.classList.add('active');
                        currentChatId = parseInt(firstChat.getAttribute('data-chat-id'));
                    }
                }
            }
        }

        // Basic chat functionality
        function sendMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();

            if (message) {
                addMessage(message, 'user');
                input.value = '';

                // Simulate AI response (replace with actual API call)
                setTimeout(() => {
                    addMessage("I'm processing your request and exploring the cosmic database for answers...", 'ai');
                }, 1000);
            }
        }

        function addMessage(text, sender) {
            const chatArea = document.getElementById('chat-area');
            const messageContainer = document.createElement('div');
            messageContainer.className = `message-container ${sender}`;

            const messageBubble = document.createElement('div');
            messageBubble.className = `message-bubble ${sender}`;
            messageBubble.textContent = text;

            messageContainer.appendChild(messageBubble);
            chatArea.appendChild(messageContainer);

            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // Event listeners
        document.getElementById('sidebar-toggle').addEventListener('click', toggleSidebar);
        document.getElementById('user-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Close chat menus when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.chat-menu') && !event.target.closest('.chat-menu-btn')) {
                document.querySelectorAll('.chat-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // Load theme preference and initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const root = document.documentElement;

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // Initialize animations
            createStars();
        });
    </script>
</body>
</html>
